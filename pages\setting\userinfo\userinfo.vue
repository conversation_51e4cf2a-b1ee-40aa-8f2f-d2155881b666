<template>
	<view class="setting_userinfo">
		<cl-navbar title="我的资料" :autoBack="true" class="custom_navbar"> </cl-navbar>
		<view class="setting_body body_nav_padding">
			<view class="set_part">
				<view class="set_title">
					<text>基本信息</text>
					<!-- <text>完善资料获取11积分</text> -->
				</view>
				<view class="set_body">
					<view class="set_item">
						<text class="set_item_left">头像</text>
						<!-- #ifdef MP-ALIPAY -->
						<view class="set_item_right">
							<image :src="$t.getImgUrl(userInfo.avatar)" mode="aspectFit"
								style="width: 100rpx;height: 100rpx;border-radius: 50%;" @click="chooseImage()"></image>
						</view>
						<!-- #endif -->

						<!-- #ifndef MP-ALIPAY -->
						<view class="set_item_right">

							<imageScrop selWidth="400upx" selHeight="400upx" @upload="avatarConfirm"
								:avatarSrc="$t.getImgUrl(userInfo.avatar)"
								avatarStyle="width: 90rpx; height: 90rpx; border-radius: 100%;">
							</imageScrop>

							<u-icon name="arrow-right" size="30rpx" color="#969799" style="margin-left: 10rpx">
							</u-icon>
						</view>
						<!-- #endif -->
					</view>
					<view class="set_item">
						<text class="set_item_left">昵称</text>
						<view class="set_item_right" @click="
							form.nickname = '';
						nicknameShow = true;
						">
							<text>{{ userInfo.nickname || "设置个性昵称" }}</text>
							<u-icon name="arrow-right" size="30rpx" color="#969799" style="margin-left: 10rpx">
							</u-icon>

							<!-- <u-popup :show="nicknameShow" customStyle="{'borderRadius':'10rpx'}" bgColor="transparent" mode="center" @close="nicknameShow = false" >
								<view class="nicknamepop">
									<view class="pop_title"> 编辑昵称 </view>
									<u--input customStyle="width: 540rpx;min-height:80rpx;margin-bottom:10rpx" placeholder="请输入昵称" border="surround" v-model="form.nickname"></u--input>
									<view class="pop_btn">
										<view @click="nicknameShow = false">取消</view>
										<view @click="confirmNickname">确认</view>
									</view>
								</view>
							</u-popup> -->

							<u-popup :show="nicknameShow" mode="center" :safeAreaInsetBottom="false"
								:closeOnClickOverlay="false" @close="nicknameShow = false" closeable
								:customStyle="{ width: '80%', borderRadius: '30rpx' }">
								<view class="edit_pop">
									<view class="edit_pop_p">编辑昵称</view>
									<view class="edit_pop_tel" v-if="nicknameShow">

										<u-icon name="account" size="50rpx" :color="baseColor"></u-icon>
										<input type="text" focus v-model="form.nickname" placeholder="请输入昵称">
									</view>

									<view class="edit_pop_btn" :style="{ backgroundColor: baseColor }"
										@click="confirmNickname">
										确认
									</view>

								</view>
							</u-popup>
						</view>
					</view>
					<view class="set_item">
						<text class="set_item_left">用户名</text>
						<view class="set_item_right">
							<text>{{ userInfo.username }}</text>
						</view>
					</view>
					<view class="set_item" @click="openSex">
						<text class="set_item_left">性别</text>
						<view class="set_item_right">
							<text>{{ userInfo.sex == 1 ? "男" : userInfo.sex == 2 ? "女" : "" }}</text>
							<u-icon name="arrow-right" size="30rpx" color="#969799" style="margin-left: 10rpx">
							</u-icon>
						</view>
					</view>
					<u-action-sheet :actions="sexOption" ancelText="取消" @close="sexCancel" @select="sexSelect"
						title="性别选择" :show="sexShow"></u-action-sheet>

					<view class="set_item" @click="choosewechatewm">
						<text class="set_item_left">微信二维码</text>
						<view class="set_item_right">
							<image :src="$t.getImgUrl(userInfo.wx_lx_ewm)" mode="aspectFill"
								v-show="userInfo.wx_lx_ewm"></image>
							<u-icon name="arrow-right" size="30rpx" color="#969799" style="margin-left: 10rpx">
							</u-icon>
						</view>
					</view>
					<template v-if="pluginInfo.ltskt == 1">


						<addressChoose ref="addressChoose" :level="4" @confirm="confirmAddress"></addressChoose>

						<u-datetime-picker ref="datetimePicker" :minDate="minDate" :formatter="formatter"
							:show="birthdayShow" v-model="birthdayValue" mode="date" confirmColor="#32B778"
							cancelColor="#999999" @cancel="birthdayShow = false" @confirm="confirmBirthday">
						</u-datetime-picker>

						<u-popup :show="signShow" mode="center" :safeAreaInsetBottom="false"
							:closeOnClickOverlay="false" @close="signShow = false" closeable
							:customStyle="{ width: '80%', borderRadius: '30rpx' }">
							<view class="edit_pop" v-if="signShow">
								<view class="edit_pop_p">编辑签名</view>
								<u--textarea style="width: 94%;margin: 0 auto;" v-model="signValue"
									placeholder="请输入签名"></u--textarea>
								<view class="edit_pop_btn" :style="{ backgroundColor: baseColor }" @click="saveSign">
									确认
								</view>

							</view>
						</u-popup>


						<view class="set_item" @click="openBirthday">
							<text class="set_item_left">生日</text>
							<view class="set_item_right">
								<text>{{ userInfo.birthday && userInfo.birthday != 0 ? $u.timeFormat(userInfo.birthday *
									1000, "yyyy-mm-dd") : '' }}</text>
								<u-icon name="arrow-right" size="30rpx" color="#969799" style="margin-left: 10rpx">
								</u-icon>
							</view>
						</view>
						<view class="set_item" @click="openLocation">
							<text class="set_item_left">所在地区</text>
							<view class="set_item_right">
								<text>{{ `${userInfo.province || ''}${userInfo.city || ''}${userInfo.area ||
									''}${userInfo.town || ''}` || '' }}</text>
								<u-icon name="arrow-right" size="30rpx" color="#969799" style="margin-left: 10rpx">
								</u-icon>
							</view>
						</view>
						<view class="set_item" @click="openSign">
							<text class="set_item_left">签名</text>
							<view class="set_item_right">
								<text>{{ userInfo.signature }}</text>
								<u-icon name="arrow-right" size="30rpx" color="#969799" style="margin-left: 10rpx">
								</u-icon>
							</view>
						</view>

					</template>


				</view>
			</view>
		</view>

	</view>
</template>

<script>
import imageScrop from "@/pages/setting/components/image_crop.vue";
import addressChoose from "@/components/common/address_choose.vue";
export default {
	data() {
		return {
			signValue: "",
			signShow: false,

			birthdayValue: "",
			birthdayShow: false,
			minDate: new Date(1950, 0, 0).getTime(),
			form: {
				nickname: "",
			},
			nicknameShow: false,
			sexShow: false,
			sexOption: [
				{ name: "男", val: 1 },
				{ name: "女", val: 2 },
			],
		};
	},
	onLoad() {
		this.$store.dispatch("getUserInfo");
	},
	components: {
		imageScrop,
		addressChoose
	},
	onReady() {
		// 微信小程序需要用此写法
		if (this.pluginInfo.ltskt == 1) {
			this.$refs.datetimePicker.setFormatter(this.formatter)
		}
	},
	methods: {
		saveSign() {
			let params = {
				signature: this.signValue
			}
			this.saveUserInfo(params);
			this.signShow = false;
		},
		openSign() {
			this.signValue = this.userInfo.signature || "";
			this.signShow = true;
		},
		confirmBirthday(val) {
			let params = {
				birthday: val.value / 1000
			}
			this.birthdayShow = false;
			this.saveUserInfo(params);
		},
		openBirthday() {
			this.birthdayValue = this.userInfo.birthday ? this.userInfo.birthday * 1000 : "";
			this.birthdayShow = true;
		},
		formatter(type, value) {
			if (type === 'year') {
				return `${value}年`
			}
			if (type === 'month') {
				return `${value}月`
			}
			if (type === 'day') {
				return `${value}日`
			}
			if (type === 'hour') {
				return `${value}时`
			}
			if (type === 'minute') {
				return `${value}分`
			}
			return value
		},
		confirmAddress(val) {
			if (val[0]) {
				let params = {
					province: val[0] || "",
					city: val[1] || "",
					area: val[2] || "",
					town: val[3] || "",
				}
				this.saveUserInfo(params);
			}

		},

		openLocation() {
			this.$refs.addressChoose.init();
		},
		openSex() {
			uni.showActionSheet({
				itemList: ['男', '女'],
				success: (res) => {
					this.saveUserInfo({ sex: res.tapIndex + 1 })
				}
			})
		},
		chooseImage() {
			this.$t.chooseImgUpload().then((url) => {
				let params = {
					avatar: url || "",
				};
				this.saveUserInfo(params);
			});
		},
		choosewechatewm() {
			uni.chooseImage({
				count: 1, //默认9
				sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
				sourceType: ["camera "], //手机拍照
				success: (res) => {
					const tempFilePaths = res.tempFilePaths;
					let BASEURL = this.$configInfo[process.env.NODE_ENV].baseUrl;
					BASEURL = process.env.VUE_APP_PLATFORM == "h5" ? "/api" : BASEURL;
					uni.uploadFile({
						url: `${BASEURL}/common/upload/index`,
						filePath: tempFilePaths[0],
						name: "file",
						success: (response) => {
							this.$t.toast("上传成功");
							let result = JSON.parse(response.data);
							let params = {
								wx_lx_ewm: result.result || "",
							};
							this.saveUserInfo(params);
						},
						fail: (err) => {
							this.$t.toast("上传失败");
						},
					});
				},
			});
		},
		confirmNickname() {
			let params = {
				nickname: this.form.nickname,
			};
			if (this.form.nickname.length > 6) {
				this.$t.toast("昵称最多六个中文");
				return false;
			}
			this.saveUserInfo(params);
			this.nicknameShow = false;
		},
		sexSelect(val) {
			let params = {
				sex: val.val,
			};
			this.saveUserInfo(params);
			this.sexShow = false;
		},
		sexCancel() {
			this.sexShow = false;
		},
		saveUserInfo(params) {
			this.$api.getUser.saveUserInfo(params).then((res) => {
				if (res.code == 200) {
					this.$t.toast("保存成功");
					this.$store.dispatch("getUserInfo").then((res) => {

					})
				}
			});
		},
		avatarConfirm(image) {
			let BASEURL = this.$configInfo[process.env.NODE_ENV].baseUrl;
			BASEURL = process.env.VUE_APP_PLATFORM == "h5" ? "/api" : BASEURL;
			uni.uploadFile({
				url: `${BASEURL}/common/upload/index`,
				filePath: image.path,
				name: "file",
				success: (response) => {
					this.$t.toast("上传成功");
					let result = JSON.parse(response.data);
					if (result.code == 200) {
						let params = {
							avatar: result.result || "",
						};
						this.saveUserInfo(params);
					} else {
						this.$t.toast(result.result);
					}
				},
				fail: (err) => {
					this.$t.toast("上传失败");
				},
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.setting_userinfo {
	padding: 0 20rpx;

	.setting_body {
		width: auto;
		box-sizing: content-box;
	}

	.set_part {
		width: auto;
		margin-top: 20rpx;

		.set_title {
			width: auto;
			padding-top: 20rpx;
			@include flex-center(column, center, flex-start);

			>text:nth-of-type(1) {
				font-size: 30rpx;
				line-height: 32rpx;
				color: #000;
				font-weight: bold;
			}

			>text:nth-of-type(2) {
				margin-top: 10rpx;
				font-size: 26rpx;
				line-height: 30rpx;
				color: #999;
			}
		}

		.set_body {
			width: auto;
			background-color: #fff;
			border-radius: 20rpx;
			margin-top: 20rpx;
			padding: 10rpx 30rpx;

			.set_item:last-of-type {
				border-bottom: none !important;
			}

			.set_item {
				width: auto;
				padding: 20rpx 0;
				min-height: 100rpx;
				border-bottom: 1px solid #f2f3f5;
				@include flex-center(row, space-between, center);

				.set_item_left {
					font-size: 30rpx;
					line-height: 32rpx;
					font-weight: bold;
					color: #323233;
				}

				.set_item_right {
					@include flex-center(row, flex-end, center);

					>text {
						color: #969799;
						text-align: right;
						@include line_overflow(400rpx);
					}

					>image {
						width: 90rpx;
						height: 90rpx;
						// width: 90rpx;
						// height: 90rpx;
						// border-radius: 50%;
					}
				}
			}
		}
	}

	/deep/.u-popup__content {
		border-radius: 20rpx 20rpx 0 0;
	}


	.edit_pop {
		width: 100%;
		padding: 24rpx 36rpx 30rpx 36rpx;

		.edit_pop_p {
			width: 100%;
			text-align: center;
			font-size: 34rpx;
			font-weight: bold;
			padding-bottom: 40rpx;
		}

		.edit_pop_tel {
			width: 100%;
			border-bottom: 1px solid #f4f4f4;
			padding: 20rpx;
			@include flex-center(row, flex-start, center);

			>input {
				flex: 1;
				margin-left: 20rpx;
			}
		}

		.edit_pop_btn {
			width: 100%;
			padding: 20rpx 0;
			@include flex-center(row, center, center);
			border-radius: 10rpx;
			font-weight: bold;
			color: #ffffff;
			margin-top: 30rpx;
		}
	}
}
</style>