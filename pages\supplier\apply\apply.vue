<template>
	<view class="supplier_apply body_nav_padding">
		<cl-navbar title="律所入驻" @leftClick="navbarLeftClick" class="custom_navbar"></cl-navbar>
		<view class="form_body" v-if="step == 1">
			<view class="form_part" style="margin-top: 0;"
				v-if="supplierConfig.supplier_company_title == 1 || supplierConfig.supplier_company_region == 1 || supplierConfig.supplier_company_add == 1">
				<view class="form_part_title">
					填写店铺管理人信息
				</view>
				<view class="form_item" v-if="supplierConfig.supplier_company_title == 1">
					<view class="form_item_title"><text>*</text>公司名称</view>
					<u--input placeholder="请输入公司名称" border="none" inputAlign="right" :readonly="isDisabled"
						v-model="form.title"></u--input>
				</view>

				<view class="form_item" @click="showAddress" v-if="supplierConfig.supplier_company_region == 1">
					<view class="form_item_title"><text>*</text>所属区域</view>
					<u--input placeholder="请选择地址" readonly :suffixIcon="isDisabled ? '' : 'arrow-right'"
						suffixIconStyle="color:#999;font-size:26rpx;" border="none" inputAlign="right"
						:value="`${form.province || ''}${form.city || ''}${form.area || ''}${form.town || ''}`">
					</u--input>
				</view>
				<view class="form_item" v-if="supplierConfig.supplier_company_add == 1">
					<view class="form_item_title"><text>*</text>公司地址</view>
					<u--input placeholder="请输入公司地址" border="none" inputAlign="right" :readonly="isDisabled"
						v-model="form.add"></u--input>
				</view>
			</view>
			<view class="form_part"
				v-if="supplierConfig.supplier_company_name == 1 || supplierConfig.supplier_company_card == 1 || supplierConfig.supplier_company_tel == 1">
				<view class="form_part_title">
					填写企业法人代表信息
				</view>
				<view class="form_item" v-if="supplierConfig.supplier_company_name == 1">
					<view class="form_item_title"><text>*</text>姓名</view>
					<u--input placeholder="请输入姓名" border="none" inputAlign="right" :readonly="isDisabled"
						v-model="form.name"></u--input>
				</view>
				<view class="form_item" v-if="supplierConfig.supplier_company_tel == 1">
					<view class="form_item_title"><text>*</text>电话</view>
					<u--input placeholder="请输入电话号码" border="none" inputAlign="right" :readonly="isDisabled"
						v-model="form.supplier_company_tel"></u--input>
				</view>
				<view class="form_item" v-if="supplierConfig.supplier_company_card == 1">
					<view class="form_item_title"><text>*</text>身份证号</view>
					<u--input placeholder="请输入身份证号码" border="none" inputAlign="right" :readonly="isDisabled"
						v-model="form.card"></u--input>
				</view>
			</view>

			<view class="form_checked" v-if="protocolInfo && protocolInfo.id > 0">
				<u-checkbox-group v-model="checked">
					<u-checkbox :activeColor="baseColor" name="1" size="28rpx" shape="circle"></u-checkbox>
					<view class="form_checked_tip">我已阅读并同意 <text :style="{ color: baseColor }"
							@click="$Router.push(`/pages/news/protocol/protocol?id=${protocolInfo.id}`)">《{{ protocolInfo.title }}》</text>
					</view>
				</u-checkbox-group>
			</view>

			<view class="" style="width: 200rpx;"></view>
			<view class="form_bottom">
				<view class="form_bottom_box" :class="isDisabled ? 'form_btn_disabled' : ''"
					:style="{ background: baseColor }" @click="$u.throttle(nextStep, 500)">
					{{ btnCn }}
				</view>
			</view>

		</view>

		<view class="form_body" v-else-if="step == 2">
			<view class="form_part" style="margin-top: 0;padding-bottom: 10rpx;"
				v-if="supplierConfig.supplier_company_cardpositive == 1 || supplierConfig.supplier_company_cardnegative == 1">
				<view class="form_part_piclink" v-if="supplierConfig.supplier_company_cardpositive == 1">
					<view class="piclink_title">
						点击上传带头像的身份证一面
					</view>
					<view class="piclink_box" @click="selectSinglePiclink('cardpositive')">
						<image
							:src="$t.getImgUrl(form.cardpositive || 'https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/supplier/supplier_apply_card1.png')"
							mode="widthFix"></image>
					</view>
				</view>
				<view class="form_part_piclink" v-if="supplierConfig.supplier_company_cardnegative == 1">
					<view class="piclink_title">
						点击上传带国徽的身份证一面
					</view>
					<view class="piclink_box" @click="selectSinglePiclink('cardnegative')">
						<image
							:src="$t.getImgUrl(form.cardnegative || 'https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/supplier/supplier_apply_card2.png')"
							mode="widthFix"></image>
					</view>
				</view>
			</view>

			<view class="" style="width: 200rpx;"></view>
			<view class="form_bottom">
				<view class="form_bottom_box" :class="isDisabled ? 'form_btn_disabled' : ''"
					:style="{ background: baseColor }" @click="$u.throttle(nextStep, 500)">
					{{ btnCn }}
				</view>
			</view>
		</view>
		<view class="form_body" v-else-if="step == 3">
			<view class="form_part" style="margin-top: 0;padding-bottom: 10rpx;"
				v-if="supplierConfig.supplier_company_license == 1 || supplierConfig.supplier_company_check == 1">
				<view class="form_part_piclink" v-if="supplierConfig.supplier_company_license == 1">
					<view class="piclink_title">
						点击上传清晰的正面的营业执照原件照片
					</view>
					<view class="piclink_box" @click="selectSinglePiclink('license')">
						<image
							:src="$t.getImgUrl(form.license || 'https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/supplier/supplier_apply_yyzz.png')"
							mode="widthFix"></image>
					</view>
				</view>
				<view class="form_part_piclink" v-if="supplierConfig.supplier_company_check == 1">
					<view class="piclink_title">
						点击上传清晰的厂家授权原件照片
					</view>
					<view class="piclink_box" @click="selectSinglePiclink('supplier_company_check')">
						<image
							:src="$t.getImgUrl(form.supplier_company_check || 'https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/supplier/supplier_apply_sq.png')"
							mode="widthFix"></image>
					</view>
				</view>
			</view>

			<view class="" style="width: 200rpx;"></view>
			<view class="form_bottom">
				<view class="form_bottom_box" :class="isDisabled ? 'form_btn_disabled' : ''"
					:style="{ background: baseColor }" @click="$u.throttle(nextStep, 500)">
					{{ btnCn }}
				</view>
			</view>
		</view>
		<view class="form_body" v-else>
			<view class="form_part" style="margin-top: 0;padding-bottom: 10rpx;">
				<view class="form_part_piclink">
					<view class="piclink_title">
						请上传主营商品样图
					</view>
					<view class="piclink_product">
						<view class="piclink_item" v-for="(item, i) in form.image_arr" :key="i">
							<image style="border: 2rpx solid #97befc;border-radius: 20rpx;" :src="$t.getImgUrl(item)"
								mode="aspectFill"></image>
							<u-icon v-if="!isDisabled" @click="deleteMorePiclink('image_arr', i)"
								customStyle="position: absolute;top: 0;right: 0;" name="close-circle-fill" size="36rpx"
								color="#5a97fa"> </u-icon>
						</view>
						<view class="piclink_item" @click="selectMorePiclink('image_arr')" v-if="!isDisabled">
							<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/708made/add_photo.png"
								mode="aspectFill"></image>
						</view>
					</view>

				</view>

			</view>



			<view class="" style="width: 200rpx;"></view>
			<view class="form_bottom">
				<view class="form_bottom_box" :class="isDisabled ? 'form_btn_disabled' : ''"
					:style="{ background: baseColor }" @click="$u.throttle(nextStep, 500)">
					提交
				</view>
			</view>
		</view>
		<u-modal :show="rejectShow" title="温馨提示" confirmText="我已了解" @confirm="rejectShow = false;">
			<view class="slot-content">
				<view v-if="form.shop_remark" class="rejectbox">
					<view>当前申请已被驳回</view>
					<view class="reasonText">驳回原因：{{ form.shop_remark }}</view>
					<view>请修改后重新提交</view>
				</view>
				<view v-else>当前申请已被驳回,请修改后重新提交</view>
			</view>
		</u-modal>
		<addressChoose ref="addressChoose" @confirm="confirmAddress"></addressChoose>
	</view>
</template>

<script>
import addressChoose from "@/components/common/address_choose.vue";
export default {
	data() {
		return {
			rejectShow: false,
			step: 1,
			checked: [],
			form: {
				title: "",
				province: "", //省
				city: "", //市
				area: "", //区
				town: "",
				add: "",
				name: "",
				supplier_company_tel: "",
				card: "",
				xxzfintegral: "", //线下支付送${integral}比例
				xxzffxyjb: "", //收款码支付折扣比例
				cardpositive: "",
				cardnegative: "",
				license: "", //营业执照
				supplier_company_check: "", //厂家授权原件
				image: "",
				image_arr: [],
			},
			applyInfo: {},
			protocolInfo: {},
			supplierConfig: {},
		};
	},
	computed: {
		btnCn() {
			if (this.form.is_check == 0) {
				return '审核中';
			} else if (this.form.is_check == 1) {
				return '审核通过';
			} else {
				return '下一步'
			}
		},
		isDisabled() {
			if (this.form.is_check == 0 || this.form.is_check == 1) {
				return true;
			} else {
				return false;
			}
		}
	},
	onLoad() {
		this.getApplyConfig();
	},
	components: {
		addressChoose
	},
	methods: {
		nextStep() {
			if (this.isDisabled) {
				return false;
			}
			let selectStep = (step) => {
				console.log(step)
				if (step == 2) {
					if (this.supplierConfig.supplier_company_cardpositive != 1 && this.supplierConfig.supplier_company_cardnegative != 1) {
						selectStep(3)
					} else {
						this.step = 2;
					}
				} else if (step == 3) {
					if (this.supplierConfig.supplier_company_license != 1 && this.supplierConfig.supplier_company_check != 1) {
						selectStep(4)
					} else {
						this.step = 3;
					}
				} else if (step == 4) {
					if (this.supplierConfig.supplier_company_product != 1) {
						this.submitApply()
					} else {
						this.step = 4;
					}
				}
			}
			if (this.step == 1) {
				if (this.supplierConfig.supplier_company_title == 1 && !this.form.title) {
					this.$t.toast('请输入公司名称')
				} else if (this.supplierConfig.supplier_company_region == 1 && (!this.form.province || !this.form.city || !this.form.area || !this.form.town)) {
					this.$t.toast('请选择地址')
				} else if (this.supplierConfig.supplier_company_add == 1 && !this.form.add) {
					this.$t.toast('请输入公司地址')
				} else if (this.supplierConfig.supplier_company_name == 1 && !this.form.name) {
					this.$t.toast('请输入姓名')
				} else if (this.supplierConfig.supplier_company_tel == 1 && !this.form.supplier_company_tel) {
					this.$t.toast('请输入电话号码')
				} else if (this.supplierConfig.supplier_company_card == 1 && !this.form.card) {
					this.$t.toast('请输入身份证号')
				} else if (this.checked.length == 0 && this.protocolInfo && this.protocolInfo.id) {
					this.$u.toast('请先阅读并同意相关协议')
				} else {
					selectStep(2);
				}
			} else if (this.step == 2) {
				if (this.supplierConfig.supplier_company_cardpositive == 1 && !this.form.cardpositive) {
					this.$t.toast('请上传正面身份证')
				} else if (this.supplierConfig.supplier_company_cardnegative == 1 && !this.form.cardnegative) {
					this.$t.toast('请上传反面身份证')
				} else {
					selectStep(3);
				}
			} else if (this.step == 3) {
				if (this.supplierConfig.supplier_company_license == 1 && !this.form.license) {
					this.$t.toast('请上传正面的营业执照原件照片')
				} else if (this.supplierConfig.supplier_company_check == 1 && !this.form.supplier_company_check) {
					this.$t.toast('请上传厂家授权原件照片')
				} else {
					selectStep(4);
				}
			} else if (this.step == 4) {
				if (this.supplierConfig.supplier_company_product == 1 && !(this.form.image_arr && this.form.image_arr.length > 0)) {
					this.$t.toast('请上传主营商品样图')
				} else {
					this.submitApply()
				}
			}
		},
		submitApply() {
			let params = {};
		},
		selectSinglePiclink(iden) {
			if (this.isDisabled) {
				return false;
			}
			this.$t.chooseImgUpload().then((url) => {
				this.form[iden] = url;
			});
		},
		deleteMorePiclink(iden, index) {
			this.form[iden].splice(index, 1)
		},
		selectMorePiclink(iden) {
			this.$t.chooseMoreImgUpload(6).then((result) => {
				this.form[iden] = [...this.form[iden], ...result]
			});
		},
		submitApply() {
			let params = {};
			params = JSON.parse(JSON.stringify(this.form));
			if (params.image_arr && params.image_arr.length > 0) {
				params.image = params.image_arr.join("@");
				delete params.image_arr
			} else {
				params.image = ""
				delete params.image_arr
			}
			this.$api.getSupplier.submitSupplierApply(params).then((res) => {
				if (res.code == 200) {
					this.$t.toast("申请已提交，等待管理员审核", 1000);
					setTimeout(() => {
						this.getApplyConfig()
						this.step = 1;
					}, 500);
				}
			});
		},
		confirmAddress(val) {
			if (val[0]) this.form.province = val[0];
			if (val[1]) this.form.city = val[1];
			if (val[2]) this.form.area = val[2];
			if (val[3]) this.form.town = val[3];
		},
		showAddress() {
			if (this.isDisabled) {
				return false;
			}
			this.$refs.addressChoose.init();
		},
		getApplyConfig() {
			this.$api.getSupplier.getSupplierConfig({}).then(res => {
				if (res.code == 200) {
					let config1 = res.result.config
					let config2 = {};
					for (const key in config1) {
						if (Object.hasOwnProperty.call(config1, key)) {
							const element = config1[key];
							config2[key] = element.value
						}
					}
					this.supplierConfig = config2;
					this.protocolInfo = res.result.protocol
					if (res.result && res.result.content && res.result.content.id) {
						let content = res.result.content;
						content.image_arr = [];
						content.image.forEach((item, i) => {
							content.image_arr.push(item.piclink)
						})
						delete content.image;
						this.form = content;
						if (this.form.is_check == 2) {
							this.rejectShow = true;
						}
					}
				} else if (res.result && res.result.info == '已经是供应商无需再审核') {
					this.$Router.replace('/pages/supplier/')
				} else { }
			})
		},
		navbarLeftClick() {
			console.log(111111)
			if (this.step == 1) {
				this.$t.gotoBack()
			} else {
				this.step = this.step - 1;
			}
		},
	}
}
</script>

<style lang="scss" scoped>
.supplier_apply {
	width: 100%;

	.form_part_piclink {
		width: 100%;
		margin-bottom: 60rpx;
		@include flex-center(column, center, center);

		.piclink_title {
			font-size: 28rpx;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #858586;
			margin: 50rpx 0 30rpx 0;
		}

		.piclink_box {
			width: 100%;
			padding: 0 90rpx;

			>image {
				width: 100%;
				height: auto;
			}
		}

		.piclink_product {
			width: 100%;
			@include flex-center(row, flex-start, center, wrap);

			.piclink_item {
				width: 200rpx;
				height: 200rpx;
				margin-right: 30rpx;
				margin-bottom: 30rpx;
				position: relative;

				>image {
					width: 100%;
					height: 100%;
				}
			}

			.piclink_item_no {
				font-size: 24rpx;
				color: #000;
			}
		}
	}


	.form_btn_disabled {
		opacity: 0.7;
	}

	.form_checked {
		@include flex-center(row, center, center);
		margin: 30rpx 0;

		.form_checked_tip {
			font-size: 24rpx;
			color: #323233;

			>text {
				color: #ed6c00;
			}
		}
	}
}

.slot-content {
	width: 100%;
	color: #606266;
	font-size: 30rpx;
	margin-top: 10rpx;

	.rejectbox {
		width: 100%;
		padding: 0 20rpx;
		@include flex-center(column, flex-start, flex-start);

		.reasonText {
			width: 100%;
			margin: 10rpx 0;
			word-break: break-all;
			@include flex-center(row, flex-start, center, wrap);
		}
	}
}
</style>