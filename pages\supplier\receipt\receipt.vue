<template>
	<view class="receipt">
		<z-paging bgColor="#F09640" ref="paging" v-model="dataList" @query="queryList" :refresher-enabled="false">
			<view slot="top">
				<cl-navbar bgColor="#F09640" leftIconColor="#FFF" :titleStyle="{ color: '#fff' }" :boder="false"
					title="律所收款二维码" :autoBack="true" :fixed="false" class="custom_navbar"></cl-navbar>
			</view>
			<view class="receipt_body">
				<view class="receipt_box">
					<view class="receipt_box_top">
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/page/receipt_money.png"
							mode="widthFix"></image>
						二维码收款
					</view>
					<view class="receipt_box_content">
						<view class="r1">打开扫一扫，向商家付款</view>
						<view class="r2" v-if="is_money">￥{{ $u.priceFormat(money, 2) }}</view>
						<view class="receipt_box_content_img">
							<image :src="painterUrl" mode="aspectFit" style="width: 380rpx;height:380rpx;"></image>
						</view>
						<view class="receipt_box_content_set">
							<view class="r3" @click="setMoney">{{ is_money ? '清除金额' : '设置金额' }}</view>
							<view class="r5" @click="saveMoney">保存收款码</view>
						</view>
					</view>
				</view>
			</view>

			<view class="collection_records">
				<view class="records_title">
					今日收款记录
					<view @click="$Router.push('/pages/order/supplierorder/supplierorder')">查看更多<u-icon color="#999999"
							size="14" name="arrow-right"></u-icon></view>
				</view>

				<view class="records_user" v-if="list && list.length != 0">
					<view class="records_item" v-for="(item, index) in list" :key="index">
						<image :src="$t.getImgUrl(item.avatar)"></image>
						<view class="records_right">
							<view class="records_box">
								<view class="records_name">{{ item.nickname || item.username || '---' }}</view>
								<view class="records_price">+{{ $u.priceFormat(item.money, 2) }}</view>
							</view>
							<view class="records_time">
								{{ $u.timeFormat(item.pay_time, "yyyy-mm-dd hh:MM") }}
							</view>
						</view>
					</view>
				</view>
				<u-empty v-else mode="data" icon="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/empty_data.png"
					text="暂无收款记录"></u-empty>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
		</z-paging>
		<receiptpainter :info="userInfo" :money="money" @onChange="getPainterUrl"
			style="position: fixed; top: 1000%; left: 1000%" v-if="painterShow"></receiptpainter>
		<u-popup :show="painterShareShow" @close="painterShareShow = false" mode="center" :closeOnClickOverlay="false"
			:closeable="true" bgColor="transparent">
			<image :src="painterUrl" mode="widthFix" style="width: 600rpx; background-color: #fff"></image>
			<pupopDownloadTips :piclink="painterUrl" @success="painterShareShow = false"></pupopDownloadTips>

		</u-popup>
	</view>
</template>

<script>
import receiptpainter from '@/pages/supplier/components/receipt_share.vue';
import pupopDownloadTips from "@/components/common/popup_download_tips.vue"
export default {
	data() {
		return {
			painterShow: false,
			loadingShow: true,
			refresherStatus: 0,
			dataList: [],
			painterUrl: '',
			money: 0,
			is_money: false,
			painterShareShow: false,
			list: [],
		};
	},
	computed: {},
	components: { receiptpainter, pupopDownloadTips },
	onShow() {
		this.painterShow = false;
		if (this.$Route.query.money) {
			this.money = this.$Route.query.money;
			this.is_money = true;
		}
		setTimeout(() => {
			this.painterShow = true
		}, false)
	},
	onLoad() {
		this.getSupplierOrderRecord();
	},
	methods: {
		getSupplierOrderRecord() {
			this.$api.getSupplier.supplierOrderRecord({}).then((res) => {
				if (res.code == 200) {
					this.list = res.result;
				}
			});
		},
		saveMoney() {
			this.painterShareShow = true;
		},
		setMoney() {
			if (this.$Route.query.money) {
				this.$Router.replace('/pages/supplier/receipt/receipt');
			} else {
				this.$Router.replace('/pages/supplier/receiptset/receiptset');
			}
		},
		getPainterUrl(imgUrl) {
			this.painterUrl = imgUrl;
		},
		queryList(pageNo, pageSize) {
			this.loadingShow = false;
		}
	}
};
</script>

<style lang="scss">
.receipt {
	width: 100%;

	/deep/.u-border-bottom {
		border-bottom: none;
	}

	.receipt_body {
		width: 100%;
		color: #ffffff;
	}

	.receipt_box {
		width: 91%;
		margin: 40rpx auto 30rpx auto;
		background-color: #ffffff;
		border-radius: 14rpx;
		overflow: hidden;
	}

	.receipt_box_top {
		width: 94%;
		padding: 0 10rpx;
		height: 100rpx;
		margin: 0 auto;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		font-size: 32rpx;
		font-family: PingFang SC-Bold, PingFang SC;
		font-weight: bold;
		color: #333333;
		border-bottom: 1rpx solid #F1F1F1;

		>image {
			width: 34rpx;
			margin-right: 20rpx;
		}
	}

	.receipt_box_top .van-icon {
		padding-right: 10rpx;
	}

	.receipt_box_content {
		width: 60%;
		margin: 0 auto;
	}

	.r1 {
		font-size: 26rpx;
		font-weight: bold;
		color: #333333;
		text-align: center;
		line-height: 72rpx;
		padding-top: 40rpx;
		padding-bottom: 30rpx;
	}

	.r2 {
		font-size: 72rpx;
		color: #000000;
		font-weight: bold;
		text-align: center;
		line-height: 72rpx;
		padding-bottom: 20rpx;
	}

	.receipt_box_content_img {
		width: 100%;
		margin: 0 auto;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.receipt_box_content_set {
		width: 80%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 0 auto;
		padding: 50rpx 0;
	}

	.r3 {
		font-size: 28rpx;
		color: #999999;
		position: relative;
		padding-right: 55rpx;
	}

	.r3::after {
		content: "";
		position: absolute;
		top: 50%;
		right: 20rpx;
		transform: translateY(-50%);
		width: 3rpx;
		height: 26rpx;
		background: #CBCBCB;
	}

	.r5 {
		font-size: 28rpx;
		color: #999999;
	}

	.upload_shaer {
		width: 100%;
		background-color: transparent;
	}

	.upload_shaer_img {
		position: relative;
		width: 70%;
		margin: 0 auto;
	}

	.upload_shaer_img i {
		position: absolute;
		bottom: 20rpx;
		right: 20rpx;
	}

	.imgUrl {
		display: block;
		max-width: 100%;
		margin: 0 auto;
	}

	.upload_shaer_btn {
		padding-top: 20rpx;
		color: #fff;
		font-size: 28rpx;
		position: relative;
		text-align: center;
	}

	.upload_shaer_btn .upload_img_ewm {
		display: block;
		width: 68rpx;
		margin: 0 auto;
	}

	.upload_shaer_btn span {
		display: inline-block;
		margin-top: 40rpx;
	}

	.collection_records {
		background: #FFFFFF;
		border-radius: 14rpx 14rpx;
		width: 91%;
		margin: 0rpx auto;
		margin-bottom: 50rpx;

		.records_title {
			padding-left: 30rpx;
			padding-top: 30rpx;
			padding-right: 20rpx;
			@include flex-center(row, space-between, center);

			>view {
				font-size: 26rpx;
				font-weight: 500;
				color: #999999;
				@include flex-center(row, flex-start, center);
			}
		}

		.records_user {
			padding: 0rpx 30rpx 0 17rpx;
			@include flex-center(column, flex-start, center);

			.records_item {
				width: 100%;
				padding: 30rpx 0;
				@include flex-center(row, flex-start, flex-start);

				>image {
					width: 60rpx;
					height: 60rpx;
					border-radius: 50%;
					flex-shrink: 0;
					margin-right: 20rpx;
				}

				.records_right {
					width: 100%;
					flex: 1;

					.records_box {
						font-size: 28rpx;
						font-weight: 500;
						color: #333333;
						width: 100%;
						margin-bottom: 8rpx;
						@include flex-center(row, space-between, center);

						.records_name {
							@include text-overflow(100%, 1);
						}

						.records_price {
							font-size: 34rpx;
							font-weight: 800;
							color: #333333;
						}
					}

					.records_time {
						font-size: 26rpx;
						font-weight: 500;
						color: #999999;
					}
				}
			}
		}
	}
}
</style>