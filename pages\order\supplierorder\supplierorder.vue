<template>
	<view class="orderlist body_nav_padding">
		<z-paging ref="paging" v-model="dataList" @query="queryList" :default-page-size="5" :auto="false"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar :autoBack="true" :fixed="false" class="custom_navbar" :border="false">
					<view class=" ordersearch" slot="center">
						<u-search placeholder="搜索我的订单" :showAction="false" v-model="oid"
							@search="orderSearch"></u-search>
					</view>
				</cl-navbar>
				<view class="orderlist_tab">
					<u-tabs lineWidth="55" lineHeight="10" :lineColor="`url(${lineBG1})`" :list="tabOption"
						:disabled="true" :current="current" @click="tabClick"
						:activeStyle="{ color: '#006AFC', 'font-size': '28rpx' }"
						:inactiveStyle="{ color: '#1A1A1A', 'font-size': '28rpx' }"
						:itemStyle="{ height: '76rpx', minWidth: '20%' }"></u-tabs>
				</view>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="orderlist_body">
				<view class="orderlist_item" v-for="(item, i) in dataList" :key="i">
					<orderSupplierItem :isSupplier="true" :info="item" @init="initOrderItem"
						@delete="deleteOrderItem(i)">
					</orderSupplierItem>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
import orderSupplierItem from "@/pages/order/components/order_item.vue"
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			current: 0,
			oid: "",
			lineBG1: 'https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/orderlist_icon.png',
		};
	},
	computed: {
		tabOption() {
			let refresherStatus = this.refresherStatus
			return [
				{ name: '全部', val: '', disabled: refresherStatus === 0 ? false : true },
				{ name: '待付款', val: '待付款', disabled: refresherStatus === 0 ? false : true },
				{ name: '待分配', val: '待发货', disabled: refresherStatus === 0 ? false : true },
				{ name: '服务中', val: '未完成', disabled: refresherStatus === 0 ? false : true },
				{ name: '已完成', val: '已完成', disabled: refresherStatus === 0 ? false : true },

			]
		}
	},
	components: {
		orderSupplierItem
	},
	onReady(query) {
		if (this.$Route.query.status) {
			this.current = this.tabOption.findIndex((item, i) => {
				return item.val == this.$Route.query.status;
			})
		}
		this.$refs.paging.reload(false);
	},
	methods: {
		initOrderItem(info) {
			let index = this.dataList.findIndex((item, i) => {
				return item.id == info.id
			})
			this.$set(this.dataList, index, info)
		},
		deleteOrderItem(index) {
			this.dataList.splice(index, 1);
		},
		orderSearch(val) {
			this.$refs.paging.reload(true);
		},
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			if (this.tabOption[this.current]) {
				params.status = this.tabOption[this.current].val
			}
			if (this.oid) {
				params.oid = this.oid;
			}
			this.$api.getSupplier.getSuplierOrderList(params).then((res) => {
				if (res.code == 200) {
					this.$refs.paging.complete(res.result);
				} else {
					this.$refs.paging.complete(false);
				}
			});
		},
		tabClick(val, index) {
			this.current = val.index;
			this.$refs.paging.reload(true);
		},
	}
}
</script>

<style lang="scss">
.orderlist {
	::v-deep {
		.u-navbar__content {
			margin-top: 10rpx;
			background-color: #f3f3f3;
		}
	}

	.ordersearch {
		width: 100%;
		padding: 0 20rpx 0 80rpx;
		border-radius: 70rpx;

		::v-deep {}
	}

	.orderlist_tab {
		width: 100%;

		::v-deep {
			.u-tabs__wrapper__nav__line {
				bottom: 15rpx !important;
				border-radius: 0 !important;
				background-size: contain !important;
				background-repeat: no-repeat !important;
				background-attachment: fixed !important;
				background-position: right !important;
			}
		}
	}

	.orderlist_body {
		padding: 0 20rpx 100rpx 20rpx;
	}
}
</style>