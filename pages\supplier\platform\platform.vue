<template>
	<view class="supplier">
		<view class="supplier_top_box" :style="{
			'min-height': ($u.getPx('474rpx') + statusBarHeight) + 'px',
			'padding-top': ($u.getPx('40rpx') + statusBarHeight) + 'px',
			background: 'url(https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_bg.png) no-repeat center top/100% 100%'
		}">
			<view class="user_nav">
				<!-- #ifndef MP -->
				<u-icon name="arrow-left" color="#333" size="35rpx" @click="$t.gotoBack()"></u-icon>
				<!-- #endif -->
				<view class="nav_right">
					<u-icon size="35rpx" name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/newUNI/icon%20(12).png"
						@click="$Router.push('/pages/supplier/receipt/receipt')"></u-icon>
					<u-icon size="35rpx" name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/newUNI/icon%20(11).png"
						@click="$t.gotoLink('/pages/home/<USER>/notice')"></u-icon>
				</view>
			</view>
			<view class="user_box">
				<view class="user_left">
					<image class="img_box"
						:src="$t.getImgUrl(userInfo.avatar || 'https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/avatar_1.png')"
						mode="aspectFill"></image>
				</view>
				<view class="user_name">
					<view class="name">{{ userInfo.nickname || userInfo.username }}</view>
					<view class="tag_box">
						<view class="tag">
							<u-icon size="24rpx"
								name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/platform_1.png"></u-icon>
							<text>律师</text>
						</view>
					</view>
				</view>

				<view class="user_localtion" @click="$Router.push('/pages/supplier/editinfo/editinfo')">
					福州市
				</view>
			</view>
			<view class="supplier_top_money" scroll-x="true" style="white-space: nowrap">
				<view class="money_box">
					<view class="money_item yknumber" @click="$Router.push('/pages/order/supplierorder/supplierorder')">
						<text>{{ info.order_quantity || 0 }}</text>
						<text>服务中</text>
					</view>
					<view class="money_item yknumber" @click="$Router.push('/pages/user/team/team')">
						<text>{{ info.consultation_volume || 0 }}</text>
						<text>已完成</text>
					</view>
				</view>
			</view>
		</view>
		<view class="supplier_body">
			<view class="shop_sy">
				<view class="sy_title">
					<view>
						<u-icon size="34rpx"
							name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/newUNI/icon.png"></u-icon>
						我的收益
					</view>
					<u-button color="#FEF6D6"
						@click="$Router.push('/pages/pay/cashflow/cashflow?iden=supply')">立即查看</u-button>
				</view>
				<view class="sy_item_box">
					<view class="sy_item" @click="$Router.push('/pages/pay/cashflow/cashflow?iden=supply')">
						<text>{{ info.closed_payment || 0 }}</text>
						<view>
							累计服务款
							<u-icon size="24rpx" name="arrow-right" top="1rpx"></u-icon>
						</view>
					</view>
					<view class="sy_item" @click="$Router.push('/pages/order/supplierorder/supplierorder?status=已完成')">
						<text>{{ info.sum_supply || 0 }}</text>
						<view>
							已结服务款
							<u-icon size="24rpx" name="arrow-right" top="1rpx"></u-icon>
						</view>
					</view>


					<view class="sy_item" @click="$Router.push('/pages/order/supplierorder/supplierorder?status=未完成')">
						<text>{{ info.outstanding_payment || 0 }}</text>
						<view>
							未结服务款
							<u-icon size="24rpx" name="arrow-right" top="1rpx"></u-icon>
						</view>
					</view>
				</view>
			</view>
			<view class="card">
				<view class="card_item" @click="$Router.push('/pages/order/supplierorder/supplierorder?status=待付款')">
					<view class="card_item_icon">
						<u-icon size="50rpx"
							name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/order_1.png"></u-icon>
						<u-badge numberType="overflow" type="error" max="99" color="#fff" :absolute="true"
							:offset="[-2, -5]" :value="info.my_order_num_1"></u-badge>
					</view>
					<text>待付款</text>
				</view>
				<view class="card_item" @click="$Router.push('/pages/order/supplierorder/supplierorder?status=未完成')">
					<view class="card_item_icon">
						<u-icon size="50rpx"
							name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/order_2.png"></u-icon>
						<u-badge numberType="overflow" type="error" max="99" color="#fff" :absolute="true"
							:offset="[-2, -5]" :value="info.my_order_num_2"></u-badge>
					</view>
					<text>服务中</text>
				</view>
				<view class="card_item" @click="$Router.push('/pages/order/supplierorder/supplierorder?status=已完成')">
					<view class="card_item_icon">
						<u-icon size="50rpx"
							name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/order_4.png"></u-icon>
						<u-badge numberType="overflow" type="error" max="99" color="#fff" :absolute="true"
							:offset="[-2, -5]" :value="info.my_order_num_3"></u-badge>
					</view>
					<text>已完成</text>
				</view>

				<view class="card_item" @click="$Router.push('/pages/order/supplierorder/supplierorder')">
					<u-icon size="50rpx"
						name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/order_5.png"></u-icon>
					<text>全部订单</text>
				</view>
			</view>
			<view class="card_dz">
				<view class="card_dz_header">
					<view>累计服务订单</view>
					<view @click="$Router.push('/pages/order/supplierorder/supplierorder')">
						点击查看
						<u-icon name="arrow-right" size="24rpx" color="#999999"></u-icon>
					</view>
				</view>
				<view class="card_dz_content">
					<view class="card_dz_content_left">
						<view>累计订单</view>
						<view>2306</view>
						<view @click="$Router.push('/pages/order/supplierorder/supplierorder')">查看详情</view>
					</view>
					<view class="card_dz_content_right">
						<view class="card_dz_content_right_item">
							<view>今日订单</view>
							<view>2306</view>
						</view>
						<view class="card_dz_content_right_item">
							<view>本月订单</view>
							<view>569</view>
						</view>
					</view>
				</view>
			</view>
			<!-- <view class="card">
				<view class="card_item" @click="$Router.push('/pages/supplier/editproduct/editproduct')">
					<u-icon size="80rpx"
						name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/newUNI/icon%20(10).png"></u-icon>
					<text>发布商品</text>
				</view>
				<view class="card_item" @click="$Router.push('/pages/supplier/orderreturn/orderreturn')">
					<u-icon size="80rpx"
						name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/newUNI/icon%20(9).png"></u-icon>
					<text>审核退货</text>
				</view>
				<view class="card_item" @click="$Router.push('/pages/shop/supplier/supplier?id=' + userInfo.id)">
					<u-icon size="80rpx"
						name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/newUNI/icon%20(8).png"></u-icon>
					<text>我的店铺</text>
				</view>
				<view class="card_item" @click="$Router.push('/pages/supplier/editlist/editlist')">
					<u-icon size="80rpx"
						name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/newUNI/icon%20(7).png"></u-icon>
					<text>管理产品</text>
				</view>
				<view class="card_item" @click="$Router.push('/pages/supplier/shop_cate_tree/shop_cate_tree')">
					<u-icon size="80rpx" name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/tree_cate.png"></u-icon>
					<text>分类管理</text>
				</view>
				 <view class="card_item">
					<u-icon size="80rpx" name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/newUNI/icon%20(6).png"></u-icon>
					<text>我要进货</text>
				</view>
		</view> -->

			<template v-if="pluginInfo.zyfh == 1">
				<view class="card_box">
					<view class="card_box_title">
						最近三日流量分析
					</view>
					<view class="card">
						<view class="card_item"
							@click="$Router.push('/pages/supplier/drag/customer_rank?sn=last_time')">
							<u-icon size="80rpx"
								name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/drag/drag_supplier_1.png"></u-icon>
							<text>最近访问</text>
						</view>
						<view class="card_item"
							@click="$Router.push('/pages/supplier/drag/customer_rank?sn=view_time')">
							<u-icon size="80rpx"
								name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/drag/drag_supplier_2.png"></u-icon>
							<text>时长排行</text>
						</view>
						<view class="card_item" @click="$Router.push('/pages/supplier/drag/customer_rank?sn=hit')">
							<u-icon size="80rpx"
								name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/drag/drag_supplier_3.png"></u-icon>
							<text>次数排行</text>
						</view>
						<view class="card_item" @click="$Router.push('/pages/supplier/drag/customer_rank?sn=product')">
							<u-icon size="80rpx"
								name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/drag/drag_supplier_4.png"></u-icon>
							<text>资源排行</text>
						</view>
					</view>
				</view>

				<view class="card_box">
					<view class="card_box_title">
						数据看板
					</view>
					<view class="card_box_data">
						<view class="data_item">
							<text>总点击量</text>
							<text>{{ dragInfo.num_1 }}</text>
						</view>
						<view class="data_item">
							<text>今日点击</text>
							<text>{{ dragInfo.num_2 }}</text>
						</view>
						<view class="data_item">
							<text>昨日点击</text>
							<text>{{ dragInfo.num_3 }}</text>
						</view>
					</view>
				</view>
				<view class="card_box">
					<view class="card_box_title">
						流量图表
					</view>
					<view class="" style="padding:30rpx 0">
						<supplierPlatformLine></supplierPlatformLine>
					</view>
				</view>
			</template>

			<view class="" style="width:100%;height:100rpx;"></view>
		</view>
	</view>
</template>

<script>
import supplierPlatformLine from "@/pages/supplier/drag/supplier_platform_line"
export default {
	data() {
		return {
			dragInfo: {},
			info: {},
		};
	},
	components: {
		supplierPlatformLine
	},
	computed: {
		wxHeight() {
			// #ifdef MP-WEIXIN
			return true;
			// #endif
			// #ifndef MP-WEIXIN
			return false;
			// #endif
		},
	},
	onLoad() {
		this.getSupplierMember();
		this.getUserInfo();
		if (this.pluginInfo.zyfh == 1) {
			this.$api.getDrag.getDragSupplierInfo({}).then(res => {
				if (res.code == 200) {
					this.dragInfo = res.result
				}
			})
		}
	},
	methods: {
		getSupplierMember() {
			this.$api.getSupplier.getSupplierMember({}).then((res) => {
				if (res.code == 200) {
					this.info = res.result;
				}
			});
		},
		getUserInfo() {
			this.$api.getUser.getUserInfo({}).then((res) => {
				if (res.code == 200) {
					this.$t.setuserInfo(res.result);
					this.$store.dispatch('getCartNumber')
				}
			});
		},
	}
}
</script>

<style lang="scss" scoped>
page {
	background-color: #f5f7fb;
}

.supplier {
	.supplier_top_box {
		padding: 0 25rpx;
		min-height: 474rpx;

		.user_nav {
			@include flex-center(row, flex-start, center);

			.nav_right {
				margin-left: auto;
				display: flex;

				/deep/.u-icon {
					margin-left: 27rpx;
				}
			}
		}

		.user_box {
			margin-top: 61rpx;
			// display: flex;
			@include flex-center(row, center, center);
			font-size: 24rpx;

			.user_left {
				.img_box {
					width: 102rpx;
					height: 102rpx;
					border-radius: 381rpx;
				}
			}

			.user_name {
				flex: 1;
				margin: 0 20rpx;

				.name {
					font-size: 32rpx;
					font-weight: bold;
					color: #0e1225;
				}

				.tag_box {
					margin-top: 20rpx;
					display: flex;
					line-height: 1;
					color: #3D4557;

					.tag {
						display: flex;
						align-items: center;
						justify-content: center;
						height: 38rpx;
						background: #d1e5fb;
						border-radius: 19rpx;
						padding: 4rpx 14rpx;

						>.u-icon {
							margin-right: 10rpx;
						}
					}
				}
			}

			.user_more {
				display: flex;
				color: #414652;
			}

			.user_localtion {
				font-weight: 500;
				font-size: 26rpx;
				color: #F3F0DC;
				padding: 8rpx 25rpx;
				background: linear-gradient(99deg, #6F768E 0%, #182944 100%);
				border-radius: 43rpx;
			}
		}

		.supplier_top_money {
			overflow: auto;

			.money_box {
				width: 100%;
				@include flex-center(row, space-around, center);

				padding: 30rpx 0;

				.money_item {
					flex: 1;
					@include flex-center(column, center, center);

					>text:nth-of-type(1) {
						font-size: 34rpx;
						padding: 0 16rpx;
						font-weight: 500;
						line-height: 36rpx;
					}

					>text:nth-of-type(2) {
						font-size: 24rpx;
						color: #999999;
						line-height: 1;
						padding-top: 10rpx;
					}
				}
			}
		}
	}

	.supplier_body {
		margin-top: -100rpx;
		z-index: 2;
		position: relative;
		padding: 0 25rpx;

		.shop_sy {
			border-radius: 16rpx;
			overflow: hidden;

			.sy_title {
				height: 68rpx;
				padding: 0 20rpx;
				@include flex-center(row, space-between, center);
				font-size: 30rpx;
				background: url('https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/newUNI/top_bg.png') no-repeat center top/100% 100%;
				color: #fef6d6;

				/deep/.u-button {
					color: #7d6c4b !important;
					font-size: 24rpx;
					width: 116rpx;
					height: 42rpx;
					padding: 0;
					margin: 0;
					border-radius: 50rpx;
				}

				>view {
					display: flex;

					/deep/.u-icon {
						margin-right: 20rpx;
					}
				}
			}

			.sy_item_box {
				background-color: #373d52;
				font-size: 30rpx;
				display: flex;

				.sy_item {
					padding: 20rpx;
					flex: 1;

					>text {
						font-size: 30rpx;
						font-weight: bold;
						color: #fef6d6;
					}

					>view {
						margin-top: 10rpx;
						display: flex;
						align-items: center;
						font-size: 24rpx;
						color: #939390;
						line-height: 1;

						.u-icon {}
					}

					position: relative;

					&:not(:last-child)::after {
						position: absolute;
						content: '';
						width: 2rpx;
						height: 60rpx;
						background: linear-gradient(180deg,
								rgba(254, 246, 214, 0) 0%,
								#fcf5d5 22%,
								#f3eccf 43%,
								#e7e1c7 74%,
								rgba(229, 223, 198, 0) 100%);
						right: 0;
						top: 50%;
						transform: translateY(-50%);
					}
				}
			}
		}

		.card_box {
			background-color: #ffffff;
			border-radius: 12rpx;
			margin-top: 20rpx;

			.card_box_title {
				font-size: 28rpx;
				font-weight: 500;
				font-weight: bold;
				color: #000000;
				padding: 20rpx 25rpx 0 25rpx;
			}

			.card {
				margin-top: 0 !important;

				.card_item {
					width: 25% !important;
					flex: none !important;
				}
			}
		}

		.card_dz {
			padding: 25rpx;
			background: #FFFFFF;
			border-radius: 12rpx;
			margin-top: 20rpx;

			.card_dz_header {
				@include flex-center(row, space-between, center);

				>view:nth-child(1) {
					font-weight: bold;
					font-size: 30rpx;
					color: #071128;
				}

				>view:nth-child(2) {
					@include flex-center(row, flex-end, center);
					gap: 10rpx;
					font-weight: 500;
					font-size: 24rpx;
					color: #797B8A;

				}
			}

			.card_dz_content {
				width: 100%;
				@include flex-center(row, space-between, center);
				gap: 20rpx;
				margin-top: 20rpx;

				>view {
					flex: 1;
				}

				.card_dz_content_left {
					border-radius: 10rpx;
					padding: 24rpx;
					padding-left: 25rpx;
					background:
						url('https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/ljorder.png') no-repeat,
						linear-gradient(144deg, #F7F7F7 0%, #F7F6F9 100%);
					background-size: 107rpx 115rpx;
					background-position: right 20rpx bottom 20rpx;


					>view:nth-child(1) {
						font-weight: 500;
						font-size: 26rpx;
						color: #384052;
					}

					>view:nth-child(2) {
						font-weight: bold;
						font-size: 38rpx;
						color: #071128;
						margin-top: 20rpx;
					}

					>view:nth-child(3) {
						width: fit-content;
						font-weight: 800;
						font-size: 24rpx;
						color: #9698A2;
						margin-top: 40rpx;
						padding: 10rpx 20rpx;
						border-radius: 25rpx;
						border: 1rpx solid #9698A2;
					}
				}

				.card_dz_content_right {
					flex: 1;
					@include flex-center(column, null, null);
					gap: 15rpx;

					.card_dz_content_right_item {
						width: 100%;
						padding: 20rpx;
						padding-left: 25rpx;
						background-size: 67rpx 68rpx !important;
						background-position: right 20rpx bottom 50% !important;
						border-radius: 10rpx;

						>view:nth-child(1) {
							font-weight: 500;
							font-size: 26rpx;
							color: #384052;
						}

						>view:nth-child(2) {
							font-weight: bold;
							font-size: 28rpx;
							color: #071128;
							margin-top: 5rpx;
						}

						&:nth-child(1) {
							background: url('https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/jrorder.png') no-repeat,
								linear-gradient(144deg, #F7F7F7 0%, #F7F6F9 100%);
						}

						&:nth-child(2) {
							background: url('https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/yueorder.png') no-repeat,
								linear-gradient(144deg, #F7F7F7 0%, #F7F6F9 100%);

						}
					}
				}
			}
		}

		.card {
			display: flex;
			padding: 30rpx 0;
			background-color: #ffffff;
			border-radius: 12rpx;
			margin-top: 20rpx;
			overflow: hidden;
			line-height: 1;

			.card_item {
				position: relative;
				@include flex-center(row, center, center);
				flex-direction: column;
				flex: 1;
				font-size: 24rpx;
				font-weight: 500;
				color: #414652;

				.card_item_icon {
					position: relative;
				}

				>text {
					margin-top: 16rpx;
				}
			}
		}

		.card_box_data {
			padding: 30rpx 25rpx;
			@include flex-center(row, space-between, center);

			.data_item {
				width: 200rpx;
				background-color: #f6f7f9;
				@include flex-center(column, flex-start, flex-start);
				padding: 20rpx;
				border-radius: 10rpx;

				>text:nth-of-type(1) {
					font-size: 28rpx;
				}

				>text:nth-of-type(2) {
					font-size: 34rpx;
					font-weight: bold;
					margin-top: 10rpx;
				}
			}
		}
	}
}
</style>