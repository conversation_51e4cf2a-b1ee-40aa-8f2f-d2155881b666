<template>
	<view class="setting_index">
		<cl-navbar title="设置" :autoBack="true" :fixed="true" class="custom_navbar"></cl-navbar>
		<view class="setting_body body_nav_padding">
			<view class="setting_part">
				<view class="setting_item" @click="$Router.push('/pages/setting/userinfo/userinfo')">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_1.png"
							mode="widthFix"></image>
						<text>我的资料</text>
					</view>
					<view class="">
						<!-- <text>已设置</text> -->
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
				<!-- #ifdef MP-WEIXIN -->
				<view class="setting_item" @click="gotoBindPhone">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_2.png"
							mode="widthFix"></image>
						<text>手机号码</text>
					</view>
					<view class="">
						<text>{{ userInfo.tel ? userInfo.tel.replace(/^(\d{3})\d{4}(\d{4})$/, "$1****$2") : '未绑定' }}</text>
						<u-icon v-if="!userInfo.tel" name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifndef MP-WEIXIN -->
				<view class="setting_item" @click="$Router.push('/pages/user/bindPhone/bindPhone')">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_2.png"
							mode="widthFix"></image>
						<text>修改手机号码</text>
					</view>
					<view class="">
						<text>{{ userInfo.tel ? userInfo.tel.replace(/^(\d{3})\d{4}(\d{4})$/, "$1****$2") : '未绑定' }}</text>
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
				<!-- #endif -->

				<view class="setting_item" @click="$Router.push('/pages/setting/realname/realname')">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_5.png"
							mode="widthFix"></image>
						<text>实名认证</text>
					</view>
					<view class="">
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
				<view class="setting_item" @click="$Router.push('/pages/setting/addresslist/addresslist')">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_8.png"
							mode="widthFix"></image>
						<text>地址管理</text>
					</view>
					<view class="">
						<text>{{ statusInfo.is_have_address === true ? "已设置" : statusInfo.is_have_address === false ?
							"未设置" : "" }}</text>
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
			</view>
			<view class="setting_part">
				<view class="setting_item" @click="$Router.push('/pages/setting/loginpassword/loginpassword')">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_6.png"
							mode="widthFix"></image>
						<text>帐号安全</text>
					</view>
					<view class="">
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
				<view class="setting_item" @click="$Router.push('/pages/setting/paypassword/paypassword')">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_7.png"
							mode="widthFix"></image>
						<text>交易密码</text>
					</view>
					<view class="">
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>

				<view class="setting_item" @click="$Router.push('/pages/setting/setwechat/setwechat')"
					v-if="isApplyWithdraw">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_3.png"
							mode="widthFix"></image>
						<text>关联微信</text>
					</view>
					<view class="">
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
				<view class="setting_item" @click="$Router.push('/pages/setting/setalipay/setalipay')"
					v-if="isApplyWithdraw">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_4.png"
							mode="widthFix"></image>
						<text>关联支付宝</text>
					</view>
					<view class="">
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
				<view class="setting_item" @click="$Router.push('/pages/setting/receivingaccount/receivingaccount')"
					v-if="isApplyWithdraw">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_9.png"
							mode="widthFix"></image>
						<text>收款账号管理</text>
					</view>
					<view class="">
						<text>{{ statusInfo.is_have_bank === true ? "已设置" : statusInfo.is_have_bank === false ? "未设置" :
							"" }}</text>
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
			</view>

			<view class="setting_part">
				<view class="setting_item" @click="$Router.push('/pages/news/protocol/protocol?id=' + item.id)"
					v-for="(item, i) in protocol" :key="i">
					<view>
						<image
							:src="i % 2 ? 'https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_10.png' : 'https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_11.png'"
							mode="widthFix"></image>
						<text>{{ item.title }}</text>
					</view>
					<view class="">
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
				<!-- <view class="setting_item">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_11.png" mode="widthFix"></image>
						<text>隐私协议</text>
					</view>
					<view class="">
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view> -->
				<view class="setting_item" @click="$t.toast('当前版本' + configInfo.version)">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_12.png"
							mode="widthFix"></image>
						<text>当前版本</text>
					</view>
					<view class="">
						<text>当前版本{{ configInfo.version }}</text>
						<u-icon name="arrow-right" size="26rpx" color="#909399"></u-icon>
					</view>
				</view>
				<!-- #ifdef APP -->
				<view class="setting_item">
					<view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/setting_11.png"
							mode="widthFix"></image>
						<text>定向推送</text>
					</view>
					<view class="">
						<u-switch v-model="value3" active-color="#971916"></u-switch>
					</view>
				</view>
				<!-- #endif -->
			</view>
			<!-- #ifdef APP -->
			<view class="setting_btn" @click="toCancel"> 注销 </view>
			<!-- #endif -->
			<view class="setting_btn" @click="logout"> 退出登录 </view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isApplyWithdraw: false, //是否允许提现
			configInfo: {},
			protocol: [],
			statusInfo: {},
			value3: false,
		};
	},
	onShow() { },
	onLoad() {
		this.$api.getUser.getUserInfoFlag({}).then(res => {
			this.loadingShow = false;
			if (res.code == 200) {
				let flag = JSON.parse(res.result.flag);
				if (flag.m_tx && flag.m_tx.data && flag.m_tx.data.tx) {
					this.isApplyWithdraw = true;
				}
			}
		})
		this.configInfo = this.$configInfo
		this.getProtocol();
		this.getStatusInfo();
	},
	computed: {},
	methods: {
		gotoBindPhone() {
			// #ifdef MP-WEIXIN
			if (this.userInfo.tel) {
				this.$t.toast('已绑定手机号')
			} else {
				this.$Router.push('/pages/user/bindPhone/bindPhone')
			}
			// #endif
			// #ifndef MP-WEIXIN
			// #endif
		},
		toCancel() {
			uni.showModal({
				title: "您是否确定注销账号",
				content: "开始注销后三个月后账号自动清除\n（所有相关数据全部清空）",
				success: (res) => {
					if (res.confirm) {
						setTimeout(() => {
							this.$t.toast('注销成功')
						}, 1000);
					} else if (res.cancel) {
						console.log("用户点击取消");
					}
				},
			});
		},
		getProtocol() {
			this.$api.getConfig.getLoginConfig({ cate: "vue_login_bg" }).then((res) => {
				if (res.code == 200) {
					this.protocol = res.result.footer;
				}
			});
		},
		getStatusInfo() {
			this.$api.getUser.getUserInfoStatus({}).then((res) => {
				if (res.code == 200) {
					this.statusInfo = res.result;
				}
			});
		},
		logout() {
			uni.showModal({
				title: "温馨提示",
				content: "是否确认退出",
				success: (res) => {
					if (res.confirm) {
						this.$store.dispatch("logout");
						// #ifndef MP-WEIXIN
						this.$t.gotoHome(2);
						// #endif
					} else if (res.cancel) {
						console.log("用户点击取消");
					}
				},
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.setting_index {
	width: 100%;

	.setting_body {
		box-sizing: content-box;

		.setting_part {
			margin-top: 20rpx;

			.setting_item:last-of-type {
				border-bottom: none !important;
			}

			.setting_item {
				width: auto;
				line-height: 1.2;
				height: 100rpx;
				@include flex-center(row, space-between, center);
				padding: 20rpx 30rpx;
				background-color: #fff;
				border-bottom: 2rpx solid #f4f4f4;

				>view:nth-of-type(1) {
					width: auto;
					@include flex-center(row, flex-start, center);

					>image {
						width: 50rpx;
						height: auto;
						margin-right: 20rpx;
					}

					>text {
						font-size: 30rpx;
					}
				}

				>view:nth-of-type(2) {
					width: auto;
					@include flex-center(row, flex-end, center);

					>text {
						font-size: 26rpx;
						color: #909399;
						margin-right: 10rpx;
					}
				}
			}
		}

		.setting_btn {
			width: 100%;
			margin-top: 20rpx;
			height: 100rpx;
			color: #f00635;
			background-color: #fff;
			font-size: 32rpx;
			font-weight: 500;
			@include flex-center(row, center, center);
		}
	}
}
</style>