<template>
	<view class="orderlist body_nav_padding">
		<z-paging ref="paging" v-model="dataList" @query="queryList" :default-page-size="5" :auto="false"
			:refresher-status.sync="refresherStatus">
			<view slot="top">

				<view class="" style="width:100%;background-color: #ffffff;"
					:style="{ height: statusBarHeight ? statusBarHeight + 'px' : 'auto' }"></view>
				<view class="orderlist_search">
					<view
						:style="{ height: navigationBarHeight ? navigationBarHeight + 'px' : 'auto', width: menuButtonLeft + 'px' }">
						<u-icon @click="$t.gotoBack()" name="arrow-left" color="#303133" size="24"></u-icon>
						<u-search placeholder="搜索我的订单" :showAction="false" v-model="title"
							@search="orderSearch"></u-search>
					</view>
				</view>

				<view class="orderlist_tab">
					<u-tabs lineWidth="55" lineHeight="10" :lineColor="`url(${lineBG1})`" :list="tabOption"
						:disabled="true" :current="current" @click="tabClick"
						:activeStyle="{ color: '#006AFC', 'font-size': '28rpx' }"
						:inactiveStyle="{ color: '#1A1A1A', 'font-size': '28rpx' }"
						:itemStyle="{ height: '76rpx', minWidth: '20%' }">
					</u-tabs>
				</view>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="orderlist_body">
				<view class="orderlist_item" v-for="(item, i) in dataList" :key="i">
					<orderItem :info="item" @init="initOrderItem" @delete="deleteOrderItem(i)"></orderItem>
				</view>
			</view>
		</z-paging>
		<payStatus ref="payStatus"></payStatus>
	</view>
</template>

<script>
import orderItem from "@/pages/order/components/order_item.vue"
import payStatus from "@/pages/order/components/pay_status.vue"
export default {
	data() {
		return {
			current: 0,
			title: "",
			dataList: [],
			refresherStatus: 0,
			lineBG1: 'https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/orderlist_icon.png',

		};
	},
	computed: {
		tabOption() {
			let refresherStatus = this.refresherStatus
			return [
				{ name: '全部', val: '', disabled: refresherStatus === 0 ? false : true },
				{ name: '待付款', val: '未支付', disabled: refresherStatus === 0 ? false : true },
				{ name: '待发货', val: '已支付', disabled: refresherStatus === 0 ? false : true },
				{ name: '待收货', val: '已发货', disabled: refresherStatus === 0 ? false : true },
				{ name: '待评价', val: '待评价', disabled: refresherStatus === 0 ? false : true },
				{ name: '退货/售后', val: '已退单', disabled: refresherStatus === 0 ? false : true },
			]
		}
	},
	components: {
		orderItem,
		payStatus
	},
	onReady(query) {
		if (this.$Route.query.status) {
			this.current = this.tabOption.findIndex((item, i) => {
				return item.val == this.$Route.query.status;
			})
		}
		this.$refs.paging.reload(false);
	},
	methods: {
		initOrderItem(info) {
			let index = this.dataList.findIndex((item, i) => {
				return item.id == info.id
			})
			this.$set(this.dataList, index, info)
		},
		deleteOrderItem(index) {
			this.dataList.splice(index, 1);
		},
		orderSearch(val) {
			this.$refs.paging.reload(false);
		},
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			if (this.tabOption[this.current]) {
				params.status = this.tabOption[this.current].val
			}
			if (this.title) {
				params.oid = this.title;
			}
			if (this.$Route.query.types) {
				params.types = this.$Route.query.types;
			}
			this.$api.getOrder.getOrderList(params).then((res) => {
				if (res.code == 200) {
					this.$refs.paging.complete(res.result);
				} else {
					this.$refs.paging.complete(false);
				}
			});
		},
		tabClick(val, index) {
			this.current = val.index;
			this.$refs.paging.reload(false);
		},
	}
};
</script>

<style lang="scss">
.orderlist {
	.orderlist_search {
		width: 100%;
		background-color: #ffffff;
		padding: 10rpx 0;
		/* #ifndef H5*/
		padding-top: 0;

		/* #endif */
		>view {
			display: flex;
			padding-left: 20rpx;
			padding-right: 20rpx;
		}
	}

	.orderlist_tab {
		width: 100%;

		/deep/.u-tabs__wrapper__nav__item {
			padding: 0 40rpx !important;
		}

		/deep/.u-tabs__wrapper__nav__line {
			// bottom: 10rpx !important;
			// background: linear-gradient(90deg, #d3201e, #e9636c 41%, #fff5f5 100%) !important;
			// background: url('https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/order/order_line.png') no-repeat !important;
			// width: 25rpx  !important;
			// height: 5rpx  !important;
			// background-size: 100% 100%;
		}

		::v-deep {
			.u-tabs__wrapper__nav__line {
				bottom: 15rpx !important;
				border-radius: 0 !important;
				background-size: contain !important;
				background-repeat: no-repeat !important;
				background-attachment: fixed !important;
				background-position: right !important;
			}
		}
	}

	.orderlist_body {
		padding: 0 20rpx 100rpx 20rpx;
	}
}
</style>