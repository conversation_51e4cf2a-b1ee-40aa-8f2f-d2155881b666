<template>
	<view class="address_list">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f3f3f3"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="地址管理" :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
				<!-- #ifdef MP-WEIXIN -->
				<view class="address_wechat" @click="wechatChooseAddress">
					<u-icon name="weixin-fill" color="#07c160" size="46rpx"></u-icon>
					<text>一键获取微信地址</text>
					<u-icon name="arrow-right" color="#999" size="32rpx"></u-icon>
				</view>
				<!-- #endif -->

			</view>
			<view class="address_list_body">
				<u-empty icon="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/address_empty.png" width="185"
					height="157" textColor="#999999" text="暂无地址，去添加吧~" v-if="dataList && dataList.length == 0"
					custom-style="margin:80rpx">
				</u-empty>
				<u-radio-group v-model="nowDefault" placement="column">
					<view class="address_item" v-for="(item, i) in dataList" :key="item.id">
						<view class="item_top" @click="useAddress(item)">
							<text>姓名：{{ item.name }}</text>
							<text>电话：{{ item.tel }}</text>
						</view>
						<view class="item_address" @click="useAddress(item)">
							<text>
								{{ `${item.province || ""} ${item.city || ""} ${item.area || ""} ${item.town || ""}` }}
							</text>
							<text>{{ item.address }}</text>
						</view>
						<view class="item_radio">
							<view @click="defaultChange(item.id)">
								<u-radio @change="defaultChange(item.id)" :name="item.id"
									:activeColor="baseColor"></u-radio>
								<text v-show="item.id == nowDefault" :style="{ color: baseColor }">默认地址</text>
								<text v-show="item.id != nowDefault">设置默认地址</text>
							</view>
							<view @click="editAddress(item)">
								<u-icon name="edit-pen-fill" size="40rpx"></u-icon>
								编辑
							</view>
							<view @click="deleteAddress(item.id)">
								<u-icon name="trash-fill" size="40rpx"></u-icon>
								删除
							</view>
						</view>
					</view>
				</u-radio-group>
				<view class="" style="width: 100%;height: 200rpx;">

				</view>
			</view>
			<view class="" slot="bottom">
				<view class="address_list_bottom">
					<view class="address_btn" :style="{ backgroundColor: baseColor }" @click="addBtn">
						新增地址 </view>
				</view>
			</view>
		</z-paging>

	</view>
</template>

<script>
export default {
	data() {
		return {
			isLoading: true,
			nowDefault: -1,
			formShow: false,
			addressList: [],
			refresherStatus: 0,
			dataList: [],
		};
	},
	components: {},
	onShow() {
		this.getAddressList();
	},
	watch: {},
	methods: {
		wechatChooseAddress() {
			let that = this;
			uni.chooseAddress({
				success(address) {
					let params = {};
					params.name = address.userName;
					params.tel = address.telNumber;
					params.province = address.provinceName;
					params.city = address.cityName;
					params.area = address.countyName;
					params.address = address.detailInfo;
					that.$api.getUser.addUserAddress(params).then((res) => {
						if (res.code == 200) {
							that.$t.toast("添加成功");
							that.getAddressList();
						}
					});
				}
			})
		},
		editAddress(item) {
			this.$Router.push(`/pages/setting/addressform/addressform?id=${item.id}`)
			// this.formShow = true;
			// this.$nextTick(() => {
			// 	this.$refs.addressForm.init(item);
			// });
		},
		useAddress(item) {
			if (this.$Route.query.from == 'orderconfirm') {
				uni.$emit('selectAddress', item)
				this.$t.gotoBack()
			} else if (this.$Route.query.from == 'yc') {
				uni.$emit('selectAddress', item)
				this.$t.gotoBack()
			} else if (this.$Route.query.from == 'shopdetail') {
				uni.$emit('updateShopAddress', item)
				this.$t.gotoBack()
			} else if (this.$Route.query.from == 'auction') {
				uni.$emit('selectAddress', item)
				this.$t.gotoBack()
			} else if (this.$Route.query.from == 'shareholder') {
				uni.$emit('selectAddress', item)
				this.$t.gotoBack()
			}
		},
		deleteAddress(id) {
			uni.showModal({
				title: "温馨提示",
				content: "是否确认删除该地址",
				success: (res) => {
					if (res.confirm) {
						this.$api.getUser.delUserAddress({ id: id }).then((res) => {
							if (res.code == 200) {
								this.$u.toast("删除地址成功");
								this.getAddressList();
								if (id == this.receiptAddress.id) {
									this.$u.vuex("receiptAddress", {});
									this.$u.vuex("selectStoreInfo", {});
								}
							}
						});
					} else if (res.cancel) { }
				},
			});
		},
		defaultChange(val) {
			if (val != this.nowDefault) {
				let params = {
					id: val,
					is_show: 1,
				};
				this.$api.getUser.defaultUserAddress(params).then((res) => {
					if (res.code == 200) {
						this.$u.toast("设置默认地址成功");
						this.nowDefault = val;
					}
				});
			}
		},
		addBtn() {
			// this.formShow = true;
			// this.$nextTick(() => {
			// 	this.$refs.addressForm.init();
			// });
			this.$Router.push('/pages/setting/addressform/addressform')
		},
		getAddressList() {
			let compare = (prop) => {
				return function (obj1, obj2) {
					var val1 = obj1[prop];
					var val2 = obj2[prop];
					if (val1 < val2) {
						return -1;
					} else if (val1 > val2) {
						return 1;
					} else {
						return 0;
					}
				}
			}
			//获取两个经纬度之间距离
			let getDistance = (lat1, lng1, lat2, lng2) => {
				var radLat1 = lat1 * Math.PI / 180.0;
				var radLat2 = lat2 * Math.PI / 180.0;
				var a = radLat1 - radLat2;
				var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
				var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
					Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
				s = s * 6378.137; // EARTH_RADIUS;
				s = Math.round(s * 10000) / 10000;
				return s;
			}
			this.isLoading = true;
			this.$api.getUser.getUserAddress({}).then(res => {
				if (res.code == 200) {
					res.result.forEach((item, i) => {
						if (item.latitude && item.longitude && this.userNowPosition.latitude && this.userNowPosition.longitude) {
							item.distance = getDistance(item.latitude, item.longitude, this.userNowPosition.latitude, this.userNowPosition.longitude)
						} else {
							item.distance = -1;
						}
					})
					let arr = res.result.sort(compare('distance'))
					var obj = arr.find((item, i) => {
						return item.is_show == 1;
					});
					if (obj && obj.id) {
						this.nowDefault = obj.id;
					}
					this.dataList = arr;
				} else {
					this.isLoading = false;
				}
			})
		},
		queryList() { },
	},
};
</script>

<style lang="scss" scoped>
.address_list {
	width: 100%;

	.address_wechat {
		width: 100%;
		@include flex-center(row, flex-start, center);
		background-color: #ffffff;
		padding: 20rpx 20rpx;

		>text {
			font-size: 28rpx;
			color: #07c160;
			margin-right: auto;
		}
	}

	.address_list_body {
		width: 100%;
		padding-top: 20rpx;

		.address_item {
			width: auto;
			padding: 40rpx 20rpx 30rpx 20rpx;
			background-color: #fff;
			margin-bottom: 20rpx;

			.item_top {
				width: 100%;
				@include flex-center(row, flex-start, center);

				>text {
					font-size: 28rpx;
					color: #000;
					font-weight: 600;
					margin-right: 40rpx;
				}
			}

			.item_address {
				font-size: 26rpx;
				font-weight: normal;
				padding: 10rpx 0 30rpx 0;
				border-bottom: 1px solid #f3f3f3;
				@include flex-center(column, flex-start, flex-start);

				>text:nth-of-type(1) {
					font-weight: 600;
				}

				>text:nth-of-type(2) {
					padding-top: 10rpx;
				}
			}

			.item_radio {
				width: 100%;
				@include flex-center(row, flex-start, center);
				padding-top: 20rpx;

				>view:nth-of-type(1) {
					flex: 1;
					@include flex-center(row, flex-start, center);
				}

				>view:nth-of-type(2) {
					font-size: 26rpx;
					@include flex-center(row, center, center);
				}

				>view:nth-of-type(3) {
					font-size: 26rpx;
					margin-left: 20rpx;
					@include flex-center(row, center, center);
				}
			}
		}
	}

	.address_list_bottom {
		width: 100%;
		background-color: #fff;
		position: relative;
		box-shadow: 0px -4rpx 12rpx 2rpx rgba(178, 178, 178, 0.16);
		padding: 20rpx 25rpx;
		padding-bottom: 20rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		@include flex-center(row, center, center);
		z-index: 99;

		.address_btn {
			flex: 1;
			height: 88rpx;
			font-size: 30rpx;
			font-weight: 400;
			color: #ffffff;
			border-radius: 10rpx;
			@include flex-center(row, center, center);
		}
	}
}
</style>