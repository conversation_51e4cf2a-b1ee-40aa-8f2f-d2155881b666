<template>
	<view class="body_nav_padding editinfo">
		<cl-navbar title="律所信息" :autoBack="true" :fixed="true" class="custom_navbar"></cl-navbar>
		<view class="editinfo_body">
			<view class="editinfo_part">
				<view class="editinfo_piclink_box" @click="chooseImage('shop_logo')">
					<view v-if="form.shop_logo == ''" class="editinfo_top_box">
						<view class="editinfo_top">
							LOGO
						</view>
						<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/page/editinfo_photo.png"></image>
					</view>
					<view class="edit_img" v-else>
						<image class="img1" :src="$t.getImgUrl(form.shop_logo)" mode="aspectFill"></image>
						<image class="img2" v-if="form.shop_logo"
							src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/page/editinfo_photo.png"></image>
					</view>
				</view>
			</view>

			<view class="editinfo_part">
				<view class="form_body">
					<view class="form_part" style="margin-top:0; padding-top: 0;">
						<view class="form_item">
							<view class="form_item_title">
								<text></text>
								律所名称
							</view>
							<u--input placeholder="请输入律所名称" border="none" inputAlign="right"
								v-model="form.shop_title"></u--input>
						</view>
						<view class="form_item" @click="pickerShow = true">
							<view class="form_item_title">
								<text></text>
								服务分类
							</view>
							<u--input suffixIconStyle="color:#999;font-size:26rpx;" suffixIcon="arrow-right" readonly
								placeholder="请选择服务分类" border="none" inputAlign="right" :value="cateTitle"></u--input>
						</view>
						<view class="form_item" @click="showAddress">
							<view class="form_item_title">
								<text></text>
								律所区域
							</view>
							<u--input placeholder="请选择区域" readonly suffixIcon="arrow-right"
								suffixIconStyle="color:#999;font-size:26rpx;" border="none" inputAlign="right"
								:value="`${form.shop_province || ''}${form.shop_city || ''}${form.shop_area || ''}${form.shop_town || ''}`"></u--input>
						</view>
						<view class="form_item">
							<view class="form_item_title">
								<text></text>
								区域街道
							</view>
							<u--input placeholder="街道门牌等信息" border="none" inputAlign="right"
								v-model="form.shop_address"></u--input>
						</view>
						<view class="form_item">
							<view class="form_item_title">
								<text></text>
								律所电话
							</view>
							<u--input placeholder="请输入律所电话" border="none" inputAlign="right"
								v-model="form.shop_tel"></u--input>
						</view>

						<view class="form_item">
							<view class="form_item_title">
								<text></text>
								是否开启语音提醒
							</view>
							<u-switch v-model="form.is_voice" :activeColor="baseColor" size="20" activeValue="1"
								inactiveValue="0"></u-switch>
						</view>
						<view class="form_item">
							<view class="form_item_title">
								<text></text>
								服务排列类型
							</view>
							<u-radio-group v-model="form.shop_chain_style" placement="row" :activeColor="baseColor">
								<u-radio label="一行两列" name="0"></u-radio>
								<u-radio style="margin-left: 20rpx;" label="一行一列" name="1"></u-radio>
							</u-radio-group>
						</view>
					</view>
				</view>

				<view class="editinfo_line"></view>

				<view class="editinfo_ewm">
					<view class="ewm_title">商户二维码</view>

					<view class="ewm_pic">
						<image @click="chooseImage('shop_wechat')" v-if="form.shop_wechat == ''"
							src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/page/editinfo__add.png"></image>
						<view class="edit_img" v-else>
							<image @click="chooseImage('shop_wechat')" :src="$t.getImgUrl(form.shop_wechat)"
								mode="aspectFill"></image>
							<u-icon v-if="form.shop_wechat" @click="form.shop_wechat = ''"
								customStyle="position: absolute;top: -15rpx;right: -15rpx;" name="close-circle-fill"
								size="36rpx" color="#FF2F57"></u-icon>
						</view>
						<view class="ewm_tip">
							请上传小于500KB的二维码图片
						</view>
					</view>
				</view>
				<view class="" style="width: 100%;height: 200rpx;"></view>
				<view class="form_bottom">
					<view class="form_bottom_box" :style="{ background: baseColor }"
						@click="$u.throttle(saveInfo, 500)">
						保存
					</view>
				</view>
			</view>
		</view>
		<addressChoose ref="addressChoose" @confirm="confirmAddress"></addressChoose>
		<u-popup zIndex="19999" :show="pickerShow" mode="bottom" @close="pickerShow = false" round="30">
			<view class="popBox">
				<view class="popShow">
					<u-button :customStyle="{ borderRadius: '30rpx 0 0 0', height: '80rpx' }" type="info" text="取消"
						@click="pickerShow = false"></u-button>
					<u-button :customStyle="{ borderRadius: '0 30rpx 0 0', height: '80rpx' }" type="success"
						color="#07c160" text="确认" @click="pickerShow = false"></u-button>
				</view>
				<view class="checkBox">
					<u-checkbox-group size="22" iconSize="16" v-model="result" :borderBottom="false" placement="column"
						iconPlacement="right">
						<u-checkbox shape="circle" :customStyle="{ margin: '13px 0' }" v-for="(item, index) in cateList"
							:key="index" :label="item.title" :name="item.id"></u-checkbox>
					</u-checkbox-group>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import addressChoose from '@/components/common/address_choose.vue';
export default {
	data() {
		return {
			form: {
				shop_title: '',
				shop_tel: '',
				shop_province: '',
				shop_logo: '',
				shop_city: '',
				shop_cate: '',
				shop_area: '',
				shop_town: '',
				shop_address: '1',
				shop_wechat: '',
				is_voice: "0"
			},
			pickerShow: false,
			result: [],
			cateList: [],
			cateTitle: ""
		};
	},
	components: {
		addressChoose
	},
	created() {
		this.getCate();
	},
	methods: {
		getCate() {
			this.$api.getSupplier.getShopCate({}).then(res => {
				if (res.code == 200) {
					this.cateList = res.result.cate;
					this.getSupplierShopS();
				}
			});
		},
		getSupplierShopS() {
			this.$api.getSupplier.getSupplierShopInfo({}).then(res => {
				if (res.code == 200) {
					this.form = res.result;
					this.result = res.result.shop_cate.split("@");
				}
			});
		},
		chooseImage(iden) {
			this.$t.chooseImgUpload().then(url => {
				this.form[iden] = url;
			});
		},
		saveInfo() {
			var that = this;
			uni.showModal({
				title: '温馨提示',
				content: '是否提交商户信息？',
				confirmText: '立即提交',
				cancelText: '我再想想',
				success: data => {
					if (data.confirm) {
						var params = {};
						params = that.form;
						this.$api.getSupplier.saveEditSupplierShopInfo(params).then(res => {
							if (res.code == 200) {
								this.$t.toast('提交成功');
								setTimeout(() => {
									this.$t.gotoBack();
								}, 500);
							}
						});
					}
				}
			});
		},
		showAddress() {
			this.$refs.addressChoose.init();
		},
		confirmAddress(val) {
			if (val[0]) this.form.shop_province = val[0];
			if (val[1]) this.form.shop_city = val[1];
			if (val[2]) this.form.shop_area = val[2];
			if (val[3]) this.form.shop_town = val[3];
		},
		getCheck(arr, data, city = []) {
			if (typeof data === "object") {
				for (let i = 0; arr[i] !== undefined; i++) {
					for (let j = 0; data[j] !== undefined; j++) {
						if (arr[i] === data[j].id) {
							city.push(data[j]);
						}
					}
				}
				for (let i = 0; data[i] !== undefined; i++) {
					this.getCheck(arr, data[i].z, city);
				}
			}
			return city;
		}
	},
	watch: {
		result(val) {
			var chenk = this.getCheck(val, this.cateList);
			this.form.shop_cate = val.join("@");
			this.cateTitle = "";
			for (var i in chenk) {
				this.cateTitle += " / " + chenk[i].title;
			}
			this.cateTitle = this.cateTitle.slice(2);
		},
	},
};
</script>

<style>
page {
	background-color: #fff;
}
</style>
<style lang="scss" scoped>
.editinfo {
	width: 100%;

	.editinfo_body {
		width: 100%;

		.editinfo_part {
			width: 100%;

			.editinfo_piclink_box {
				width: 100%;
				@include flex-center(column, center, center);
				background-color: #fff;
				padding: 50rpx 0;

				.edit_img {
					position: relative;
					border-radius: 90rpx 90rpx;
					border: 1rpx solid #F1F1F1;

					.img1 {
						width: 180rpx;
						height: 180rpx;
						border-radius: 90rpx 90rpx;
					}

					.img2 {
						width: 44rpx;
						height: 44rpx;
						position: absolute;
						right: 0;
						bottom: 0;
					}
				}

				.editinfo_top_box {
					position: relative;

					.editinfo_top {
						width: 180rpx;
						height: 180rpx;
						background: #F1F1F1;
						border-radius: 90rpx 90rpx;
						font-size: 30rpx;
						font-family: PingFang SC-Bold, PingFang SC;
						font-weight: bold;
						color: #999999;
						@include flex-center(row, center, center);
					}

					>image {
						width: 44rpx;
						height: 44rpx;
						position: absolute;
						right: 0;
						bottom: 0;
					}
				}
			}
		}

		.editinfo_line {
			height: 20rpx;
			width: 100%;
			background: #FAFAFA;
		}

		.editinfo_ewm {
			padding: 20rpx 25rpx;

			.ewm_title {
				font-size: 28rpx;
				font-weight: bold;
				color: #333333;
			}

			.ewm_pic {
				margin-top: 30rpx;

				>image {
					width: 150rpx;
					height: 150rpx;
					border-radius: 10rpx 10rpx;
				}

				.edit_img {
					position: relative;
					border-radius: 90rpx 90rpx;
					width: 150rpx;
					height: 150rpx;

					>image {
						width: 150rpx;
						height: 150rpx;
						border-radius: 10rpx 10rpx;
					}
				}

				.ewm_tip {
					font-size: 24rpx;
					font-weight: 500;
					color: #999999;
					margin-top: 30rpx;
				}
			}
		}
	}
}

.popBox {
	@include flex-center(column, flex-start, center);

	.popShow {
		width: 100%;
		@include flex-center(row, space-between, center);
	}

	.checkBox {
		width: 100%;
		padding: 20rpx 30rpx;
		overflow: auto;
		max-height: 800rpx;
	}
}
</style>