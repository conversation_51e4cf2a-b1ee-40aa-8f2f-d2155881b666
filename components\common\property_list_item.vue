<template>
	<view class="property_list_item">
		<view class="property_item_card">
			<!-- 房源图片 -->
			<view class="property_image_container">
				<image 
					class="property_image" 
					:src="propertyData.image || 'https://via.placeholder.com/200x150'" 
					mode="aspectFill"
				></image>
				<!-- 状态标签 -->
				<view class="status_tag" :class="getStatusClass()">
					<text class="status_text">{{ getStatusText() }}</text>
				</view>
			</view>
			
			<!-- 房源信息 -->
			<view class="property_info">
				<!-- 标题 -->
				<view class="property_title">{{ propertyData.title || '人气好房·东泰未来城国际东门业主精装住宅' }}</view>
				
				<!-- 房型信息 -->
				<view class="property_details">
					<text class="detail_text">{{ propertyData.rooms || '3室2厅' }}</text>
					<text class="detail_separator">|</text>
					<text class="detail_text">{{ propertyData.area || '90' }}㎡</text>
					<text class="detail_separator">|</text>
					<text class="detail_text">{{ propertyData.orientation || '朝南' }}</text>
					<text class="detail_separator">|</text>
					<text class="detail_text">{{ propertyData.district || '晋安府区' }}</text>
				</view>
				
				<!-- 标签 -->
				<view class="property_tags" v-if="propertyData.tags && propertyData.tags.length">
					<view 
						class="property_tag" 
						v-for="(tag, index) in propertyData.tags" 
						:key="index"
					>
						<text class="tag_text">{{ tag }}</text>
					</view>
				</view>
				
				<!-- 价格和操作 -->
				<view class="property_footer">
					<view class="price_container">
						<text class="price_number">{{ propertyData.price || '169' }}</text>
						<text class="price_unit">万</text>
						<text class="price_per_sqm">{{ propertyData.pricePerSqm || '18778' }}元/㎡</text>
					</view>
					
					<!-- 操作按钮 -->
					<view class="action_buttons">
						<view 
							class="action_btn" 
							:class="getActionButtonClass()" 
							@click="handleActionClick"
						>
							<text class="action_text">{{ getActionButtonText() }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PropertyListItem',
	props: {
		propertyData: {
			type: Object,
			default: () => ({
				id: '',
				image: '',
				title: '',
				rooms: '',
				area: '',
				orientation: '',
				district: '',
				price: '',
				pricePerSqm: '',
				status: '', // 待审核、已发布、已驳回
				tags: []
			})
		}
	},
	methods: {
		getStatusClass() {
			const status = this.propertyData.status;
			switch(status) {
				case '待审核':
					return 'status_pending';
				case '已发布':
					return 'status_published';
				case '已驳回':
					return 'status_rejected';
				default:
					return 'status_pending';
			}
		},
		
		getStatusText() {
			return this.propertyData.status || '待审核';
		},
		
		getActionButtonClass() {
			const status = this.propertyData.status;
			switch(status) {
				case '已发布':
					return 'action_btn_cancel';
				case '已驳回':
					return 'action_btn_retry';
				default:
					return 'action_btn_default';
			}
		},
		
		getActionButtonText() {
			const status = this.propertyData.status;
			switch(status) {
				case '已发布':
					return '取消发布';
				case '已驳回':
					return '重新发布';
				default:
					return '立即下架';
			}
		},
		
		handleActionClick() {
			this.$emit('action-click', {
				action: this.getActionButtonText(),
				data: this.propertyData
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.property_list_item {
	width: 100%;
	padding: 0 25rpx;
	margin-bottom: 20rpx;
	
	.property_item_card {
		width: 100%;
		background: #FFFFFF;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		
		.property_image_container {
			width: 100%;
			height: 300rpx;
			position: relative;
			
			.property_image {
				width: 100%;
				height: 100%;
			}
			
			.status_tag {
				position: absolute;
				top: 20rpx;
				left: 20rpx;
				padding: 8rpx 16rpx;
				border-radius: 8rpx;
				
				&.status_pending {
					background: #F63030;
				}
				
				&.status_published {
					background: #006AFC;
				}
				
				&.status_rejected {
					background: #BFBFBF;
				}
				
				.status_text {
					font-size: 22rpx;
					color: #FFFFFF;
					font-weight: 500;
				}
			}
		}
		
		.property_info {
			padding: 30rpx;
			
			.property_title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333333;
				line-height: 44rpx;
				margin-bottom: 20rpx;
				@include text-overflow(100%, 2);
			}
			
			.property_details {
				@include flex-center(row, flex-start, center);
				margin-bottom: 20rpx;
				
				.detail_text {
					font-size: 28rpx;
					color: #333333;
				}
				
				.detail_separator {
					font-size: 28rpx;
					color: #EEEEEE;
					margin: 0 16rpx;
				}
			}
			
			.property_tags {
				@include flex-center(row, flex-start, center);
				margin-bottom: 30rpx;
				gap: 16rpx;
				
				.property_tag {
					padding: 8rpx 16rpx;
					background: #F8F8F8;
					border-radius: 8rpx;
					
					.tag_text {
						font-size: 24rpx;
						color: #333333;
					}
				}
			}
			
			.property_footer {
				@include flex-center(row, space-between, center);
				
				.price_container {
					@include flex-center(row, flex-start, baseline);
					
					.price_number {
						font-size: 32rpx;
						font-weight: bold;
						color: #F63030;
					}
					
					.price_unit {
						font-size: 28rpx;
						color: #333333;
						margin-left: 4rpx;
					}
					
					.price_per_sqm {
						font-size: 26rpx;
						color: #999999;
						margin-left: 16rpx;
					}
				}
				
				.action_buttons {
					.action_btn {
						padding: 16rpx 32rpx;
						border-radius: 8rpx;
						
						&.action_btn_default {
							background: #BFBFBF;
						}
						
						&.action_btn_cancel {
							background: #BFBFBF;
						}
						
						&.action_btn_retry {
							background: #006AFC;
						}
						
						.action_text {
							font-size: 26rpx;
							color: #FFFFFF;
							font-weight: 500;
						}
					}
				}
			}
		}
	}
}
</style>
